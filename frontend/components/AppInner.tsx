import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { CheckSquare, Target } from "lucide-react";
import TaskManager from "./TaskManager";
import GoalManager from "./GoalManager";
import ThemeToggle from "./ThemeToggle";

export default function AppInner() {
  const [activeTab, setActiveTab] = useState("tasks");

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-foreground">Task & Goal Manager</h1>
        <ThemeToggle />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="tasks" className="flex items-center gap-2">
            <CheckSquare className="h-4 w-4" />
            Tasks
          </TabsTrigger>
          <TabsTrigger value="goals" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Goals
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="tasks">
          <TaskManager />
        </TabsContent>
        
        <TabsContent value="goals">
          <GoalManager />
        </TabsContent>
      </Tabs>
    </div>
  );
}
