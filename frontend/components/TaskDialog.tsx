import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import backend from "~backend/client";
import type { Task, Priority, Goal } from "~backend/shared/types";

interface TaskDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task?: Task | null;
  onTaskCreated?: () => void;
  onTaskUpdated?: () => void;
  goals: Goal[];
}

export default function TaskDialog({ open, onOpenChange, task, onTaskCreated, onTaskUpdated, goals }: TaskDialogProps) {
  const { toast } = useToast();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [dueDate, setDueDate] = useState("");
  const [priority, setPriority] = useState<Priority>("Medium");
  const [selectedGoalIds, setSelectedGoalIds] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditing = !!task;

  useEffect(() => {
    if (task) {
      setTitle(task.title);
      setDescription(task.description || "");
      setDueDate(task.dueDate ? new Date(task.dueDate).toISOString().split("T")[0] : "");
      setPriority(task.priority);
      
      // Set selected goals
      const goalIds: number[] = [];
      if (task.goalId) goalIds.push(task.goalId);
      if (task.goals) {
        task.goals.forEach(goal => {
          if (!goalIds.includes(goal.id)) {
            goalIds.push(goal.id);
          }
        });
      }
      setSelectedGoalIds(goalIds);
    } else {
      setTitle("");
      setDescription("");
      setDueDate("");
      setPriority("Medium");
      setSelectedGoalIds([]);
    }
  }, [task, open]);

  const handleGoalToggle = (goalId: number, checked: boolean) => {
    if (checked) {
      setSelectedGoalIds(prev => [...prev, goalId]);
    } else {
      setSelectedGoalIds(prev => prev.filter(id => id !== goalId));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim()) return;

    setIsSubmitting(true);
    try {
      const taskData = {
        title: title.trim(),
        description: description.trim() || undefined,
        dueDate: dueDate ? new Date(dueDate) : undefined,
        priority,
        goalIds: selectedGoalIds,
      };

      if (isEditing) {
        await backend.task.update({
          id: task.id,
          ...taskData,
        });
        toast({
          title: "Task updated",
          description: "Task has been successfully updated",
        });
        onTaskUpdated?.();
      } else {
        await backend.task.create(taskData);
        toast({
          title: "Task created",
          description: "Task has been successfully created",
        });
        onTaskCreated?.();
      }
    } catch (error) {
      console.error("Failed to save task:", error);
      toast({
        title: "Error",
        description: `Failed to ${isEditing ? "update" : "create"} task`,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Task" : "Create New Task"}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter task title"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter task description (optional)"
              rows={3}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="dueDate">Due Date</Label>
            <Input
              id="dueDate"
              type="date"
              value={dueDate}
              onChange={(e) => setDueDate(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="priority">Priority</Label>
            <Select value={priority} onValueChange={(value: Priority) => setPriority(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="High">High</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="Low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {goals.length > 0 && (
            <div className="space-y-2">
              <Label>Goals</Label>
              <div className="space-y-2 max-h-32 overflow-y-auto border rounded-md p-2">
                {goals.map((goal) => (
                  <div key={goal.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`goal-${goal.id}`}
                      checked={selectedGoalIds.includes(goal.id)}
                      onCheckedChange={(checked) => handleGoalToggle(goal.id, checked as boolean)}
                    />
                    <Label htmlFor={`goal-${goal.id}`} className="text-sm font-normal">
                      {goal.name}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          )}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={!title.trim() || isSubmitting}>
              {isSubmitting ? "Saving..." : isEditing ? "Update Task" : "Create Task"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
