import React from "react";
import { Trash2, Edit, Calendar, AlertCircle, Target } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import backend from "~backend/client";
import type { TaskWithHighlights, SearchHighlight } from "~backend/shared/types";

interface TaskListProps {
  tasks: TaskWithHighlights[];
  isLoading: boolean;
  onTaskUpdated: () => void;
  onEditTask: (task: any) => void;
}

function HighlightedText({ highlights }: { highlights: SearchHighlight[] }) {
  return (
    <>
      {highlights.map((highlight, index) => (
        <span
          key={index}
          className={highlight.highlighted ? "bg-yellow-200 dark:bg-yellow-800 font-medium" : ""}
        >
          {highlight.text}
        </span>
      ))}
    </>
  );
}

export default function TaskList({ tasks, isLoading, onTaskUpdated, onEditTask }: TaskListProps) {
  const { toast } = useToast();

  const handleToggleComplete = async (task: TaskWithHighlights) => {
    try {
      await backend.task.update({
        id: task.id,
        completed: !task.completed,
      });
      onTaskUpdated();
      toast({
        title: "Task updated",
        description: `Task marked as ${!task.completed ? "completed" : "pending"}`,
      });
    } catch (error) {
      console.error("Failed to update task:", error);
      toast({
        title: "Error",
        description: "Failed to update task",
        variant: "destructive",
      });
    }
  };

  const handleDeleteTask = async (taskId: number) => {
    try {
      await backend.task.deleteTask({ id: taskId });
      onTaskUpdated();
      toast({
        title: "Task deleted",
        description: "Task has been successfully deleted",
      });
    } catch (error) {
      console.error("Failed to delete task:", error);
      toast({
        title: "Error",
        description: "Failed to delete task",
        variant: "destructive",
      });
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "Medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "Low":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const isOverdue = (dueDate: Date) => {
    return new Date(dueDate) < new Date() && new Date(dueDate).toDateString() !== new Date().toDateString();
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-muted-foreground">No tasks found. Create your first task to get started!</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {tasks.map((task) => (
        <Card key={task.id} className={`transition-all duration-200 ${task.completed ? "opacity-75" : ""}`}>
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Checkbox
                checked={task.completed}
                onCheckedChange={() => handleToggleComplete(task)}
                className="mt-1"
              />
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2 mb-2">
                  <h3 className={`font-medium ${task.completed ? "line-through text-muted-foreground" : ""}`}>
                    <HighlightedText highlights={task.title} />
                  </h3>
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <Badge className={getPriorityColor(task.priority)}>
                      {task.priority}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditTask(task)}
                      className="h-8 w-8 p-0"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteTask(task.id)}
                      className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                {task.description && (
                  <p className={`text-sm mb-2 ${task.completed ? "line-through text-muted-foreground" : "text-muted-foreground"}`}>
                    <HighlightedText highlights={task.description} />
                  </p>
                )}
                <div className="flex items-center gap-4 text-sm">
                  {task.dueDate && (
                    <div className="flex items-center gap-1">
                      {isOverdue(task.dueDate) && !task.completed ? (
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      ) : (
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                      )}
                      <span className={`${isOverdue(task.dueDate) && !task.completed ? "text-red-500 font-medium" : "text-muted-foreground"}`}>
                        Due {formatDate(task.dueDate)}
                      </span>
                    </div>
                  )}
                  {task.goalId && (
                    <div className="flex items-center gap-1 text-muted-foreground">
                      <Target className="h-4 w-4" />
                      <span>Goal assigned</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
