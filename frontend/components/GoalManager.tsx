import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Plus, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import GoalList from "./GoalList";
import GoalDialog from "./GoalDialog";
import backend from "~backend/client";
import type { Goal } from "~backend/shared/types";

export default function GoalManager() {
  const [searchQuery, setSearchQuery] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null);

  const { data: goalsData, isLoading, refetch } = useQuery({
    queryKey: ["goals", searchQuery],
    queryFn: async () => {
      const params: any = {};
      if (searchQuery) params.search = searchQuery;
      return backend.goal.list(params);
    },
  });

  const handleGoalCreated = () => {
    setIsCreateDialogOpen(false);
    refetch();
  };

  const handleGoalUpdated = () => {
    setEditingGoal(null);
    refetch();
  };

  const handleEditGoal = (goal: Goal) => {
    setEditingGoal(goal);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search goals..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)} className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          Add Goal
        </Button>
      </div>

      <GoalList
        goals={goalsData?.goals || []}
        isLoading={isLoading}
        onGoalUpdated={refetch}
        onEditGoal={handleEditGoal}
      />

      <GoalDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onGoalCreated={handleGoalCreated}
      />

      <GoalDialog
        open={!!editingGoal}
        onOpenChange={(open) => !open && setEditingGoal(null)}
        goal={editingGoal}
        onGoalUpdated={handleGoalUpdated}
      />
    </div>
  );
}
