import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import backend from "~backend/client";
import type { Goal } from "~backend/shared/types";

interface GoalDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  goal?: Goal | null;
  onGoalCreated?: () => void;
  onGoalUpdated?: () => void;
}

export default function GoalDialog({ open, onOpenChange, goal, onGoalCreated, onGoalUpdated }: GoalDialogProps) {
  const { toast } = useToast();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [targetDate, setTargetDate] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditing = !!goal;

  useEffect(() => {
    if (goal) {
      setName(goal.name);
      setDescription(goal.description || "");
      setTargetDate(goal.targetDate ? new Date(goal.targetDate).toISOString().split("T")[0] : "");
    } else {
      setName("");
      setDescription("");
      setTargetDate("");
    }
  }, [goal, open]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) return;

    setIsSubmitting(true);
    try {
      const goalData = {
        name: name.trim(),
        description: description.trim() || undefined,
        targetDate: targetDate ? new Date(targetDate) : undefined,
      };

      if (isEditing) {
        await backend.goal.update({
          id: goal.id,
          ...goalData,
        });
        toast({
          title: "Goal updated",
          description: "Goal has been successfully updated",
        });
        onGoalUpdated?.();
      } else {
        await backend.goal.create(goalData);
        toast({
          title: "Goal created",
          description: "Goal has been successfully created",
        });
        onGoalCreated?.();
      }
    } catch (error) {
      console.error("Failed to save goal:", error);
      toast({
        title: "Error",
        description: `Failed to ${isEditing ? "update" : "create"} goal`,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Goal" : "Create New Goal"}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter goal name"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter goal description (optional)"
              rows={3}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="targetDate">Target Date</Label>
            <Input
              id="targetDate"
              type="date"
              value={targetDate}
              onChange={(e) => setTargetDate(e.target.value)}
            />
          </div>
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={!name.trim() || isSubmitting}>
              {isSubmitting ? "Saving..." : isEditing ? "Update Goal" : "Create Goal"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
