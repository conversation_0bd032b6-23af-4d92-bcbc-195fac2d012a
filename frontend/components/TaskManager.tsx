import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Plus, Search } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import TaskList from "./TaskList";
import TaskDialog from "./TaskDialog";
import backend from "~backend/client";
import type { Task } from "~backend/shared/types";

export default function TaskManager() {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterCompleted, setFilterCompleted] = useState<string>("all");
  const [filterGoal, setFilterGoal] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);

  const { data: tasksData, isLoading, refetch } = useQuery({
    queryKey: ["tasks", searchQuery, filterCompleted, filterGoal],
    queryFn: async () => {
      const params: any = {};
      if (searchQuery) params.search = searchQuery;
      if (filterCompleted !== "all") {
        params.completed = filterCompleted === "completed";
      }
      if (filterGoal !== "all") {
        params.goalId = parseInt(filterGoal);
      }
      return backend.task.list(params);
    },
  });

  const { data: goalsData } = useQuery({
    queryKey: ["goals"],
    queryFn: () => backend.goal.list({}),
  });

  const handleTaskCreated = () => {
    setIsCreateDialogOpen(false);
    refetch();
  };

  const handleTaskUpdated = () => {
    setEditingTask(null);
    refetch();
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search tasks..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={filterCompleted} onValueChange={setFilterCompleted}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Tasks</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterGoal} onValueChange={setFilterGoal}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Goals</SelectItem>
            {goalsData?.goals.map((goal) => (
              <SelectItem key={goal.id} value={goal.id.toString()}>
                {goal.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button onClick={() => setIsCreateDialogOpen(true)} className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          Add Task
        </Button>
      </div>

      <TaskList
        tasks={tasksData?.tasks || []}
        isLoading={isLoading}
        onTaskUpdated={refetch}
        onEditTask={handleEditTask}
      />

      <TaskDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onTaskCreated={handleTaskCreated}
        goals={goalsData?.goals || []}
      />

      <TaskDialog
        open={!!editingTask}
        onOpenChange={(open) => !open && setEditingTask(null)}
        task={editingTask}
        onTaskUpdated={handleTaskUpdated}
        goals={goalsData?.goals || []}
      />
    </div>
  );
}
