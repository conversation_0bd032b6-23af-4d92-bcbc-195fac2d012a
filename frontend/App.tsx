import React from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "./components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import AppInner from "./components/AppInner";

const queryClient = new QueryClient();

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="system" storageKey="task-app-theme">
        <div className="min-h-screen bg-background">
          <AppInner />
          <Toaster />
        </div>
      </ThemeProvider>
    </QueryClientProvider>
  );
}
