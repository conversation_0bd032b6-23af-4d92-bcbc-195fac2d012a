{"name": "frontend", "version": "1.0.0", "type": "module", "packageManager": "bun", "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.81.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.484.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@tailwindcss/oxide": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "lightningcss": "^1.30.1", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "vite": "^6.3.5"}, "optionalDependencies": {"rollup": "^4.44.1"}}