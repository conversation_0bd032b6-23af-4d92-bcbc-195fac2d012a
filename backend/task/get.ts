import { api, APIError } from "encore.dev/api";
import { taskDB } from "./db";
import type { Task } from "../shared/types";

interface GetTaskParams {
  id: number;
}

// Retrieves a task by ID.
export const get = api<GetTaskParams, Task>(
  { expose: true, method: "GET", path: "/tasks/:id" },
  async (params) => {
    const row = await taskDB.queryRow<{
      id: number;
      title: string;
      description: string | null;
      due_date: Date | null;
      priority: string;
      completed: boolean;
      created_at: Date;
      updated_at: Date;
      goal_id: number | null;
    }>`
      SELECT id, title, description, due_date, priority, completed, created_at, updated_at, goal_id
      FROM tasks 
      WHERE id = ${params.id}
    `;

    if (!row) {
      throw APIError.notFound("task not found");
    }

    // Get associated goals
    const goalRows = await taskDB.queryAll<{
      id: number;
      name: string;
      description: string | null;
      target_date: Date | null;
      created_at: Date;
      updated_at: Date;
    }>`
      SELECT g.id, g.name, g.description, g.target_date, g.created_at, g.updated_at
      FROM goals g
      INNER JOIN task_goals tg ON g.id = tg.goal_id
      WHERE tg.task_id = ${params.id}
    `;

    const goals = goalRows.map(goal => ({
      id: goal.id,
      name: goal.name,
      description: goal.description || undefined,
      targetDate: goal.target_date || undefined,
      createdAt: goal.created_at,
      updatedAt: goal.updated_at,
    }));

    return {
      id: row.id,
      title: row.title,
      description: row.description || undefined,
      dueDate: row.due_date || undefined,
      priority: row.priority as any,
      completed: row.completed,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      goalId: row.goal_id || undefined,
      goals: goals.length > 0 ? goals : undefined,
    };
  }
);
