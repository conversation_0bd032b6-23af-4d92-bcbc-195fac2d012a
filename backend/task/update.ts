import { api, APIError } from "encore.dev/api";
import { taskDB } from "./db";
import type { UpdateTaskRequest, Task } from "../shared/types";

interface UpdateTaskParams {
  id: number;
}

// Updates a task.
export const update = api<UpdateTaskParams & UpdateTaskRequest, Task>(
  { expose: true, method: "PUT", path: "/tasks/:id" },
  async (params) => {
    const updates: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    if (params.title !== undefined) {
      updates.push(`title = $${paramIndex}`);
      values.push(params.title);
      paramIndex++;
    }

    if (params.description !== undefined) {
      updates.push(`description = $${paramIndex}`);
      values.push(params.description || null);
      paramIndex++;
    }

    if (params.dueDate !== undefined) {
      updates.push(`due_date = $${paramIndex}`);
      values.push(params.dueDate || null);
      paramIndex++;
    }

    if (params.priority !== undefined) {
      updates.push(`priority = $${paramIndex}`);
      values.push(params.priority);
      paramIndex++;
    }

    if (params.completed !== undefined) {
      updates.push(`completed = $${paramIndex}`);
      values.push(params.completed);
      paramIndex++;
    }

    if (params.goalId !== undefined) {
      updates.push(`goal_id = $${paramIndex}`);
      values.push(params.goalId || null);
      paramIndex++;
    }

    if (updates.length === 0 && !params.goalIds) {
      throw APIError.invalidArgument("no fields to update");
    }

    if (updates.length > 0) {
      updates.push(`updated_at = NOW()`);
      values.push(params.id);

      const query = `
        UPDATE tasks 
        SET ${updates.join(", ")}
        WHERE id = $${paramIndex}
        RETURNING id, title, description, due_date, priority, completed, created_at, updated_at, goal_id
      `;

      const row = await taskDB.rawQueryRow<{
        id: number;
        title: string;
        description: string | null;
        due_date: Date | null;
        priority: string;
        completed: boolean;
        created_at: Date;
        updated_at: Date;
        goal_id: number | null;
      }>(query, ...values);

      if (!row) {
        throw APIError.notFound("task not found");
      }
    }

    // Handle many-to-many goal relationships
    if (params.goalIds !== undefined) {
      // Remove existing relationships
      await taskDB.exec`DELETE FROM task_goals WHERE task_id = ${params.id}`;
      
      // Add new relationships
      if (params.goalIds.length > 0) {
        for (const goalId of params.goalIds) {
          await taskDB.exec`
            INSERT INTO task_goals (task_id, goal_id) VALUES (${params.id}, ${goalId})
            ON CONFLICT DO NOTHING
          `;
        }
      }
    }

    // Get the updated task
    const updatedRow = await taskDB.queryRow<{
      id: number;
      title: string;
      description: string | null;
      due_date: Date | null;
      priority: string;
      completed: boolean;
      created_at: Date;
      updated_at: Date;
      goal_id: number | null;
    }>`
      SELECT id, title, description, due_date, priority, completed, created_at, updated_at, goal_id
      FROM tasks 
      WHERE id = ${params.id}
    `;

    if (!updatedRow) {
      throw APIError.notFound("task not found");
    }

    return {
      id: updatedRow.id,
      title: updatedRow.title,
      description: updatedRow.description || undefined,
      dueDate: updatedRow.due_date || undefined,
      priority: updatedRow.priority as any,
      completed: updatedRow.completed,
      createdAt: updatedRow.created_at,
      updatedAt: updatedRow.updated_at,
      goalId: updatedRow.goal_id || undefined,
    };
  }
);
