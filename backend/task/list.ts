import { api } from "encore.dev/api";
import { Query } from "encore.dev/api";
import { taskDB } from "./db";
import type { ListTasksWithHighlightsResponse, SearchHighlight } from "../shared/types";

interface ListTasksParams {
  search?: Query<string>;
  completed?: Query<boolean>;
  goalId?: Query<number>;
  limit?: Query<number>;
  offset?: Query<number>;
}

function parseHighlights(text: string, search?: string): SearchHighlight[] {
  if (!search || !text) {
    return [{ text, highlighted: false }];
  }

  const searchTerms = search.toLowerCase().split(/\s+/).filter(term => term.length > 0);
  const result: SearchHighlight[] = [];
  let currentIndex = 0;
  const textLower = text.toLowerCase();

  // Find all matches
  const matches: { start: number; end: number }[] = [];
  
  for (const term of searchTerms) {
    let index = 0;
    while ((index = textLower.indexOf(term, index)) !== -1) {
      matches.push({ start: index, end: index + term.length });
      index += term.length;
    }
  }

  // Sort matches by start position and merge overlapping ones
  matches.sort((a, b) => a.start - b.start);
  const mergedMatches: { start: number; end: number }[] = [];
  
  for (const match of matches) {
    if (mergedMatches.length === 0 || match.start > mergedMatches[mergedMatches.length - 1].end) {
      mergedMatches.push(match);
    } else {
      mergedMatches[mergedMatches.length - 1].end = Math.max(mergedMatches[mergedMatches.length - 1].end, match.end);
    }
  }

  // Build result with highlights
  for (const match of mergedMatches) {
    if (currentIndex < match.start) {
      result.push({ text: text.slice(currentIndex, match.start), highlighted: false });
    }
    result.push({ text: text.slice(match.start, match.end), highlighted: true });
    currentIndex = match.end;
  }

  if (currentIndex < text.length) {
    result.push({ text: text.slice(currentIndex), highlighted: false });
  }

  return result.length > 0 ? result : [{ text, highlighted: false }];
}

// Retrieves all tasks with semantic search and highlighting.
export const list = api<ListTasksParams, ListTasksWithHighlightsResponse>(
  { expose: true, method: "GET", path: "/tasks" },
  async (params) => {
    const limit = params.limit || 50;
    const offset = params.offset || 0;
    
    let whereClause = "WHERE 1=1";
    const queryParams: any[] = [];
    let paramIndex = 1;

    if (params.search) {
      whereClause += ` AND (
        to_tsvector('english', title) @@ plainto_tsquery('english', $${paramIndex}) OR 
        to_tsvector('english', COALESCE(description, '')) @@ plainto_tsquery('english', $${paramIndex}) OR
        title ILIKE '%' || $${paramIndex} || '%' OR
        COALESCE(description, '') ILIKE '%' || $${paramIndex} || '%'
      )`;
      queryParams.push(params.search);
      paramIndex++;
    }

    if (params.completed !== undefined) {
      whereClause += ` AND completed = $${paramIndex}`;
      queryParams.push(params.completed);
      paramIndex++;
    }

    if (params.goalId !== undefined) {
      whereClause += ` AND (goal_id = $${paramIndex} OR EXISTS (
        SELECT 1 FROM task_goals tg WHERE tg.task_id = tasks.id AND tg.goal_id = $${paramIndex}
      ))`;
      queryParams.push(params.goalId);
      paramIndex++;
    }

    const orderClause = `
      ORDER BY 
        completed ASC,
        CASE 
          WHEN due_date IS NULL THEN 1 
          ELSE 0 
        END,
        due_date ASC,
        CASE priority 
          WHEN 'High' THEN 1 
          WHEN 'Medium' THEN 2 
          WHEN 'Low' THEN 3 
        END,
        created_at DESC
    `;

    // Get total count
    const countQuery = `SELECT COUNT(*) as count FROM tasks ${whereClause}`;
    const countResult = await taskDB.rawQueryRow<{ count: number }>(countQuery, ...queryParams);
    const total = countResult?.count || 0;

    // Get tasks
    const tasksQuery = `
      SELECT id, title, description, due_date, priority, completed, created_at, updated_at, goal_id
      FROM tasks 
      ${whereClause}
      ${orderClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    const rows = await taskDB.rawQueryAll<{
      id: number;
      title: string;
      description: string | null;
      due_date: Date | null;
      priority: string;
      completed: boolean;
      created_at: Date;
      updated_at: Date;
      goal_id: number | null;
    }>(tasksQuery, ...queryParams, limit, offset);

    const tasks = rows.map(row => ({
      id: row.id,
      title: parseHighlights(row.title, params.search),
      description: row.description ? parseHighlights(row.description, params.search) : undefined,
      dueDate: row.due_date || undefined,
      priority: row.priority as any,
      completed: row.completed,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      goalId: row.goal_id || undefined,
    }));

    return { tasks, total };
  }
);
