import { api, APIError } from "encore.dev/api";
import { taskDB } from "./db";

interface DeleteTaskParams {
  id: number;
}

// Deletes a task.
export const deleteTask = api<DeleteTaskParams, void>(
  { expose: true, method: "DELETE", path: "/tasks/:id" },
  async (params) => {
    // Delete task-goal relationships first (handled by CASCADE)
    await taskDB.exec`
      DELETE FROM tasks WHERE id = ${params.id}
    `;
  }
);
