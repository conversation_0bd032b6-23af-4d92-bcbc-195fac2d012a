CREATE TABLE goals (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  target_date DATE,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE TABLE task_goals (
  task_id BIGINT NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  goal_id BIGINT NOT NULL REFERENCES goals(id) ON DELETE CASCADE,
  PRIMARY KEY (task_id, goal_id)
);

CREATE INDEX idx_goals_name ON goals USING gin(to_tsvector('english', name));
CREATE INDEX idx_goals_description ON goals USING gin(to_tsvector('english', description));
CREATE INDEX idx_goals_target_date ON goals(target_date);
CREATE INDEX idx_task_goals_task_id ON task_goals(task_id);
CREATE INDEX idx_task_goals_goal_id ON task_goals(goal_id);

-- Add goal_id column to tasks table for backward compatibility
ALTER TABLE tasks ADD COLUMN goal_id BIGINT REFERENCES goals(id) ON DELETE SET NULL;
CREATE INDEX idx_tasks_goal_id ON tasks(goal_id);
