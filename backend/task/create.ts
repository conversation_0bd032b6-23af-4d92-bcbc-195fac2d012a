import { api } from "encore.dev/api";
import { taskDB } from "./db";
import type { CreateTaskRequest, Task } from "../shared/types";

// Creates a new task.
export const create = api<CreateTaskRequest, Task>(
  { expose: true, method: "POST", path: "/tasks" },
  async (req) => {
    const row = await taskDB.queryRow<{
      id: number;
      title: string;
      description: string | null;
      due_date: Date | null;
      priority: string;
      completed: boolean;
      created_at: Date;
      updated_at: Date;
      goal_id: number | null;
    }>`
      INSERT INTO tasks (title, description, due_date, priority, goal_id)
      VALUES (${req.title}, ${req.description || null}, ${req.dueDate || null}, ${req.priority}, ${req.goalId || null})
      RETURNING id, title, description, due_date, priority, completed, created_at, updated_at, goal_id
    `;

    if (!row) {
      throw new Error("Failed to create task");
    }

    // If goalIds are provided, create many-to-many relationships
    if (req.goalIds && req.goalIds.length > 0) {
      for (const goalId of req.goalIds) {
        await taskDB.exec`
          INSERT INTO task_goals (task_id, goal_id) VALUES (${row.id}, ${goalId})
          ON CONFLICT DO NOTHING
        `;
      }
    }

    return {
      id: row.id,
      title: row.title,
      description: row.description || undefined,
      dueDate: row.due_date || undefined,
      priority: row.priority as any,
      completed: row.completed,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      goalId: row.goal_id || undefined,
    };
  }
);
