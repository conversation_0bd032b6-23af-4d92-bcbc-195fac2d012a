import { api } from "encore.dev/api";
import { goalDB } from "./db";
import type { CreateGoalRequest, Goal } from "../shared/types";

// Creates a new goal.
export const create = api<CreateGoalRequest, Goal>(
  { expose: true, method: "POST", path: "/goals" },
  async (req) => {
    const row = await goalDB.queryRow<{
      id: number;
      name: string;
      description: string | null;
      target_date: Date | null;
      created_at: Date;
      updated_at: Date;
    }>`
      INSERT INTO goals (name, description, target_date)
      VALUES (${req.name}, ${req.description || null}, ${req.targetDate || null})
      RETURNING id, name, description, target_date, created_at, updated_at
    `;

    if (!row) {
      throw new Error("Failed to create goal");
    }

    return {
      id: row.id,
      name: row.name,
      description: row.description || undefined,
      targetDate: row.target_date || undefined,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      progress: 0,
      totalTasks: 0,
      completedTasks: 0,
    };
  }
);
