import { api, APIError } from "encore.dev/api";
import { goalDB } from "./db";
import type { Goal } from "../shared/types";

interface GetGoalParams {
  id: number;
}

// Retrieves a goal by ID with progress information.
export const get = api<GetGoalParams, Goal>(
  { expose: true, method: "GET", path: "/goals/:id" },
  async (params) => {
    const row = await goalDB.queryRow<{
      id: number;
      name: string;
      description: string | null;
      target_date: Date | null;
      created_at: Date;
      updated_at: Date;
      total_tasks: number;
      completed_tasks: number;
      progress: number;
    }>`
      SELECT 
        g.id, 
        g.name, 
        g.description, 
        g.target_date, 
        g.created_at, 
        g.updated_at,
        COALESCE(task_stats.total_tasks, 0) as total_tasks,
        COALESCE(task_stats.completed_tasks, 0) as completed_tasks,
        CASE 
          WHEN COALESCE(task_stats.total_tasks, 0) = 0 THEN 0.0
          ELSE ROUND((COALESCE(task_stats.completed_tasks, 0)::NUMERIC / task_stats.total_tasks::NUMERIC) * 100, 2)
        END as progress
      FROM goals g
      LEFT JOIN (
        SELECT 
          goal_id,
          COUNT(*) as total_tasks,
          COUNT(CASE WHEN completed = true THEN 1 END) as completed_tasks
        FROM (
          SELECT tg.goal_id, t.completed
          FROM task_goals tg
          INNER JOIN tasks t ON tg.task_id = t.id
          WHERE tg.goal_id = ${params.id}
          UNION ALL
          SELECT t.goal_id, t.completed
          FROM tasks t
          WHERE t.goal_id = ${params.id}
        ) combined_tasks
        GROUP BY goal_id
      ) task_stats ON g.id = task_stats.goal_id
      WHERE g.id = ${params.id}
    `;

    if (!row) {
      throw APIError.notFound("goal not found");
    }

    return {
      id: row.id,
      name: row.name,
      description: row.description || undefined,
      targetDate: row.target_date || undefined,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      progress: row.progress,
      totalTasks: row.total_tasks,
      completedTasks: row.completed_tasks,
    };
  }
);
