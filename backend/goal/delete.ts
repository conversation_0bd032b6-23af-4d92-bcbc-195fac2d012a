import { api, APIError } from "encore.dev/api";
import { goalDB } from "./db";

interface DeleteGoalParams {
  id: number;
}

// Deletes a goal.
export const deleteGoal = api<DeleteGoalParams, void>(
  { expose: true, method: "DELETE", path: "/goals/:id" },
  async (params) => {
    // Delete goal (task-goal relationships will be handled by CASCADE)
    await goalDB.exec`
      DELETE FROM goals WHERE id = ${params.id}
    `;
  }
);
