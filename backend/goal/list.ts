import { api } from "encore.dev/api";
import { Query } from "encore.dev/api";
import { goalDB } from "./db";
import type { ListGoalsResponse } from "../shared/types";

interface ListGoalsParams {
  search?: Query<string>;
  limit?: Query<number>;
  offset?: Query<number>;
}

// Retrieves all goals with progress information.
export const list = api<ListGoalsParams, ListGoalsResponse>(
  { expose: true, method: "GET", path: "/goals" },
  async (params) => {
    const limit = params.limit || 50;
    const offset = params.offset || 0;
    
    let whereClause = "WHERE 1=1";
    const queryParams: any[] = [];
    let paramIndex = 1;

    if (params.search) {
      whereClause += ` AND (
        to_tsvector('english', g.name) @@ plainto_tsquery('english', $${paramIndex}) OR 
        to_tsvector('english', COALESCE(g.description, '')) @@ plainto_tsquery('english', $${paramIndex}) OR
        g.name ILIKE '%' || $${paramIndex} || '%' OR
        COALESCE(g.description, '') ILIKE '%' || $${paramIndex} || '%'
      )`;
      queryParams.push(params.search);
      paramIndex++;
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as count FROM goals g ${whereClause}`;
    const countResult = await goalDB.rawQueryRow<{ count: number }>(countQuery, ...queryParams);
    const total = countResult?.count || 0;

    // Get goals with progress
    const goalsQuery = `
      SELECT 
        g.id, 
        g.name, 
        g.description, 
        g.target_date, 
        g.created_at, 
        g.updated_at,
        COALESCE(task_stats.total_tasks, 0) as total_tasks,
        COALESCE(task_stats.completed_tasks, 0) as completed_tasks,
        CASE 
          WHEN COALESCE(task_stats.total_tasks, 0) = 0 THEN 0.0
          ELSE ROUND((COALESCE(task_stats.completed_tasks, 0)::NUMERIC / task_stats.total_tasks::NUMERIC) * 100, 2)
        END as progress
      FROM goals g
      LEFT JOIN (
        SELECT 
          goal_id,
          COUNT(*) as total_tasks,
          COUNT(CASE WHEN completed = true THEN 1 END) as completed_tasks
        FROM (
          SELECT tg.goal_id, t.completed
          FROM task_goals tg
          INNER JOIN tasks t ON tg.task_id = t.id
          UNION ALL
          SELECT t.goal_id, t.completed
          FROM tasks t
          WHERE t.goal_id IS NOT NULL
        ) combined_tasks
        GROUP BY goal_id
      ) task_stats ON g.id = task_stats.goal_id
      ${whereClause}
      ORDER BY g.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    const rows = await goalDB.rawQueryAll<{
      id: number;
      name: string;
      description: string | null;
      target_date: Date | null;
      created_at: Date;
      updated_at: Date;
      total_tasks: number;
      completed_tasks: number;
      progress: number;
    }>(goalsQuery, ...queryParams, limit, offset);

    const goals = rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description || undefined,
      targetDate: row.target_date || undefined,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      progress: row.progress,
      totalTasks: row.total_tasks,
      completedTasks: row.completed_tasks,
    }));

    return { goals, total };
  }
);
