import { api, APIError } from "encore.dev/api";
import { goalDB } from "./db";
import type { UpdateGoalRequest, Goal } from "../shared/types";

interface UpdateGoalParams {
  id: number;
}

// Updates a goal.
export const update = api<UpdateGoalParams & UpdateGoalRequest, Goal>(
  { expose: true, method: "PUT", path: "/goals/:id" },
  async (params) => {
    const updates: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    if (params.name !== undefined) {
      updates.push(`name = $${paramIndex}`);
      values.push(params.name);
      paramIndex++;
    }

    if (params.description !== undefined) {
      updates.push(`description = $${paramIndex}`);
      values.push(params.description || null);
      paramIndex++;
    }

    if (params.targetDate !== undefined) {
      updates.push(`target_date = $${paramIndex}`);
      values.push(params.targetDate || null);
      paramIndex++;
    }

    if (updates.length === 0) {
      throw APIError.invalidArgument("no fields to update");
    }

    updates.push(`updated_at = NOW()`);
    values.push(params.id);

    const query = `
      UPDATE goals 
      SET ${updates.join(", ")}
      WHERE id = $${paramIndex}
      RETURNING id, name, description, target_date, created_at, updated_at
    `;

    const row = await goalDB.rawQueryRow<{
      id: number;
      name: string;
      description: string | null;
      target_date: Date | null;
      created_at: Date;
      updated_at: Date;
    }>(query, ...values);

    if (!row) {
      throw APIError.notFound("goal not found");
    }

    // Get progress information
    const progressRow = await goalDB.queryRow<{
      total_tasks: number;
      completed_tasks: number;
      progress: number;
    }>`
      SELECT 
        COALESCE(task_stats.total_tasks, 0) as total_tasks,
        COALESCE(task_stats.completed_tasks, 0) as completed_tasks,
        CASE 
          WHEN COALESCE(task_stats.total_tasks, 0) = 0 THEN 0.0
          ELSE ROUND((COALESCE(task_stats.completed_tasks, 0)::NUMERIC / task_stats.total_tasks::NUMERIC) * 100, 2)
        END as progress
      FROM (
        SELECT 
          COUNT(*) as total_tasks,
          COUNT(CASE WHEN completed = true THEN 1 END) as completed_tasks
        FROM (
          SELECT t.completed
          FROM task_goals tg
          INNER JOIN tasks t ON tg.task_id = t.id
          WHERE tg.goal_id = ${params.id}
          UNION ALL
          SELECT t.completed
          FROM tasks t
          WHERE t.goal_id = ${params.id}
        ) combined_tasks
      ) task_stats
    `;

    return {
      id: row.id,
      name: row.name,
      description: row.description || undefined,
      targetDate: row.target_date || undefined,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      progress: progressRow?.progress || 0,
      totalTasks: progressRow?.total_tasks || 0,
      completedTasks: progressRow?.completed_tasks || 0,
    };
  }
);
