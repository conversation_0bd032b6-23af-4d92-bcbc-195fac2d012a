export type Priority = "High" | "Medium" | "Low";

export interface Task {
  id: number;
  title: string;
  description?: string;
  dueDate?: Date;
  priority: Priority;
  completed: boolean;
  createdAt: Date;
  updatedAt: Date;
  goalId?: number;
  goals?: Goal[];
}

export interface CreateTaskRequest {
  title: string;
  description?: string;
  dueDate?: Date;
  priority: Priority;
  goalId?: number;
  goalIds?: number[];
}

export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  dueDate?: Date;
  priority?: Priority;
  completed?: boolean;
  goalId?: number;
  goalIds?: number[];
}

export interface ListTasksRequest {
  search?: string;
  completed?: boolean;
  goalId?: number;
  limit?: number;
  offset?: number;
}

export interface ListTasksResponse {
  tasks: Task[];
  total: number;
}

export interface Goal {
  id: number;
  name: string;
  description?: string;
  targetDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  progress?: number;
  totalTasks?: number;
  completedTasks?: number;
}

export interface CreateGoalRequest {
  name: string;
  description?: string;
  targetDate?: Date;
}

export interface UpdateGoalRequest {
  name?: string;
  description?: string;
  targetDate?: Date;
}

export interface ListGoalsResponse {
  goals: Goal[];
  total: number;
}

export interface SearchHighlight {
  text: string;
  highlighted: boolean;
}

export interface TaskWithHighlights extends Omit<Task, 'title' | 'description'> {
  title: SearchHighlight[];
  description?: SearchHighlight[];
}

export interface ListTasksWithHighlightsResponse {
  tasks: TaskWithHighlights[];
  total: number;
}
